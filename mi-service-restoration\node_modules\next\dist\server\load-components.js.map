{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["evalManifestWithRetries", "loadComponents", "loadManifestWithRetries", "manifestPath", "attempts", "loadManifest", "err", "wait", "evalManifest", "loadClientReferenceManifest", "entryName", "context", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "isDev", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "requirePage", "hasClientManifest", "endsWith", "UNDERSCORE_NOT_FOUND_ROUTE", "manifestLoadAttempts", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "join", "BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "replace", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "catch", "setReferenceManifestsSingleton", "serverModuleMap", "createServerModuleMap", "pageName", "ComponentMod", "Component", "interopDefault", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "getTracer", "wrap", "LoadComponentsSpan"], "mappings": ";;;;;;;;;;;;;;;;IA8FsBA,uBAAuB;eAAvBA;;IA4ITC,cAAc;eAAdA;;IA/JSC,uBAAuB;eAAvBA;;;2BArDf;sBACc;yBACO;gCACG;wBACL;4BACS;8BACQ;sBACtB;iCAC0B;6BACT;AA4C/B,eAAeA,wBACpBC,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOC,IAAAA,0BAAY,EAAIF;QACzB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAKO,eAAeP,wBACpBG,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOI,IAAAA,0BAAY,EAAIL;QACzB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAEA,eAAeE,4BACbN,YAAoB,EACpBO,SAAiB,EACjBN,QAAiB;IAEjB,IAAI;QACF,MAAMO,UAAU,MAAMX,wBAEnBG,cAAcC;QACjB,OAAOO,QAAQC,cAAc,CAACF,UAAU;IAC1C,EAAE,OAAOJ,KAAK;QACZ,OAAOO;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EAMN;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACH,WAAW;QACb,CAACE,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,cAAcV,SAAS;YAChEM,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,SAASV,SAAS;SAC5D;IACH;IAEA,6DAA6D;IAC7D,MAAMW,oBACJT,aAAcD,CAAAA,KAAKW,QAAQ,CAAC,YAAYX,SAASY,qCAA0B,AAAD;IAE5E,0EAA0E;IAC1E,6EAA6E;IAC7E,qEAAqE;IACrE,sBAAsB;IACtB,MAAMC,uBAAuBX,QAAQ,IAAI;IAEzC,gCAAgC;IAChC,MAAM,CACJY,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMZ,QAAQC,GAAG,CAAC;QACpBpB,wBACEgC,IAAAA,UAAI,EAACnB,SAASoB,yBAAc,GAC5BN;QAEF3B,wBACEgC,IAAAA,UAAI,EAACnB,SAASqB,kCAAuB,GACrCP;QAEFH,oBACIjB,4BACEyB,IAAAA,UAAI,EACFnB,SACA,UACA,OACAC,KAAKqB,OAAO,CAAC,QAAQ,OAAO,MAAMC,oCAAyB,GAAG,QAEhEtB,KAAKqB,OAAO,CAAC,QAAQ,MACrBR,wBAEFhB;QACJI,YACIf,wBACEgC,IAAAA,UAAI,EAACnB,SAAS,UAAUwB,oCAAyB,GAAG,UACpDV,sBACAW,KAAK,CAAC,IAAM,QACd;KACL;IAED,iFAAiF;IACjF,4EAA4E;IAC5E,uCAAuC;IACvC,IAAIP,yBAAyBD,yBAAyB;QACpDS,IAAAA,+CAA8B,EAAC;YAC7BT;YACAC;YACAS,iBAAiBC,IAAAA,kCAAqB,EAAC;gBACrCV;gBACAW,UAAU5B;YACZ;QACF;IACF;IAEA,MAAM6B,eAAe,MAAMxB,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChDC,IAAAA,oBAAW,EAACT,MAAMD,SAASE;IAG7B,MAAM6B,YAAYC,IAAAA,8BAAc,EAACF;IACjC,MAAMG,WAAWD,IAAAA,8BAAc,EAAC5B;IAChC,MAAM8B,MAAMF,IAAAA,8BAAc,EAAC3B;IAE3B,MAAM,EAAE8B,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvER;IAEF,OAAO;QACLI;QACAD;QACAF;QACAhB;QACAC;QACAuB,YAAYT,aAAaU,MAAM,IAAI,CAAC;QACpCV;QACAK;QACAC;QACAC;QACApB;QACAC;QACAhB;QACAD;QACAqC;IACF;AACF;AAEO,MAAMpD,iBAAiBuD,IAAAA,iBAAS,IAAGC,IAAI,CAC5CC,8BAAkB,CAACzD,cAAc,EACjCa"}