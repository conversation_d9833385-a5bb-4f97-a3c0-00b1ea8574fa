{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/webp/webp_node_dec.js"], "names": ["<PERSON><PERSON><PERSON>", "readyPromiseResolve", "readyPromiseReject", "Promise", "resolve", "reject", "moduleOverrides", "key", "hasOwnProperty", "arguments_", "thisProgram", "quit_", "status", "toThrow", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "ENVIRONMENT_IS_NODE", "scriptDirectory", "locateFile", "path", "read_", "readBinary", "nodeFS", "nodePath", "require", "dirname", "__dirname", "shell_read", "filename", "binary", "ret", "buffer", "Uint8Array", "assert", "process", "length", "replace", "slice", "out", "console", "log", "bind", "err", "warn", "wasmBinary", "noExitRuntime", "WebAssembly", "abort", "was<PERSON><PERSON><PERSON><PERSON>", "ABORT", "EXITSTATUS", "condition", "text", "UTF8Decoder", "TextDecoder", "UTF8ToString", "ptr", "maxBytesToRead", "maxPtr", "end", "HEAPU8", "decode", "subarray", "stringToUTF8Array", "str", "heap", "outIdx", "maxBytesToWrite", "startIdx", "endIdx", "i", "u", "charCodeAt", "u1", "stringToUTF8", "outPtr", "lengthBytesUTF8", "len", "UTF16Decoder", "UTF16ToString", "endPtr", "idx", "maxIdx", "HEAPU16", "codeUnit", "HEAP16", "String", "fromCharCode", "stringToUTF16", "undefined", "startPtr", "numCharsToWrite", "lengthBytesUTF16", "UTF32ToString", "utf32", "HEAP32", "ch", "stringToUTF32", "trailSurrogate", "lengthBytesUTF32", "alignUp", "x", "multiple", "HEAP8", "HEAPU32", "HEAPF32", "HEAPF64", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "INITIAL_MEMORY", "wasmTable", "__ATPRERUN__", "__ATINIT__", "__ATPOSTRUN__", "runtimeInitialized", "preRun", "addOnPreRun", "shift", "callRuntimeCallbacks", "initRuntime", "postRun", "addOnPostRun", "cb", "unshift", "addOnInit", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "addRunDependency", "id", "removeRunDependency", "clearInterval", "callback", "what", "e", "RuntimeError", "dataURIPrefix", "isDataURI", "startsWith", "wasmBinaryFile", "Error", "getBinary", "file", "getBinaryPromise", "then", "createWasm", "info", "a", "asmLibraryArg", "receiveInstance", "instance", "module", "exports", "receiveInstantiationResult", "result", "instantiateArrayBuffer", "receiver", "instantiate", "reason", "instantiateAsync", "catch", "callbacks", "func", "arg", "get", "_atexit", "___cxa_thread_atexit", "a0", "a1", "__embind_register_bigint", "primitiveType", "name", "size", "minRange", "max<PERSON><PERSON><PERSON>", "getShiftFromSize", "TypeError", "embind_init_charCodes", "codes", "Array", "embind_charCodes", "readLatin1String", "c", "awaitingDependencies", "registeredTypes", "typeDependencies", "char_0", "char_9", "makeLegalFunctionName", "f", "createNamedFunction", "body", "Function", "extendError", "baseErrorType", "errorName", "errorClass", "message", "stack", "toString", "prototype", "Object", "create", "constructor", "BindingError", "throwBindingError", "InternalError", "throwInternalError", "whenDependentTypesAreResolved", "myTypes", "dependentTypes", "getTypeConverters", "for<PERSON>ach", "type", "onComplete", "typeConverters", "myTypeConverters", "registerType", "unregisteredTypes", "registered", "dt", "push", "rawType", "registeredInstance", "options", "ignoreDuplicateRegistrations", "__embind_register_bool", "trueValue", "falseValue", "fromWireType", "wt", "toWireType", "destructors", "o", "argPackAdvance", "readValueFromPointer", "pointer", "destructorFunction", "emval_free_list", "emval_handle_array", "value", "__emval_decref", "handle", "refcount", "count_emval_handles", "count", "get_first_emval", "init_emval", "__emval_register", "pop", "simpleReadValueFromPointer", "__embind_register_emval", "rv", "_embind_repr", "v", "t", "floatReadValueFromPointer", "__embind_register_float", "new_", "argumentList", "dummy", "obj", "r", "apply", "runDestructors", "del", "craftInvokerFunction", "humanName", "argTypes", "classType", "cppInvokerFunc", "cppTargetFunc", "argCount", "isClassMethodFunc", "needsDestructorStack", "returns", "argsList", "argsListWired", "invokerFnBody", "dtorStack", "args1", "args2", "paramName", "invokerFunction", "ensureOverloadTable", "proto", "methodName", "overloadTable", "prevFunc", "arguments", "exposePublicSymbol", "numArguments", "heap32VectorToArray", "firstElement", "array", "replacePublicSymbol", "dynCallLegacy", "sig", "args", "concat", "call", "dynCall", "includes", "getDynCaller", "<PERSON><PERSON><PERSON><PERSON>", "embind__requireFunction", "signature", "rawFunction", "makeDynCaller", "fp", "UnboundTypeError", "getTypeName", "___getTypeName", "_free", "throwUnboundTypeError", "types", "unboundTypes", "seen", "visit", "map", "join", "__embind_register_function", "rawArgTypesAddr", "rawInvoker", "fn", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "integerReadValueFromPointer", "signed", "readS8FromPointer", "readU8FromPointer", "readS16FromPointer", "readU16FromPointer", "readS32FromPointer", "readU32FromPointer", "__embind_register_integer", "bitshift", "isUnsignedType", "__embind_register_memory_view", "dataTypeIndex", "typeMapping", "TA", "decodeMemoryView", "data", "__embind_register_std_string", "stdStringIsUTF8", "decodeStartPtr", "currentBytePtr", "maxRead", "stringSegment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "valueIsOfTypeString", "Uint8ClampedArray", "_malloc", "charCode", "__embind_register_std_wstring", "charSize", "decodeString", "encodeString", "getHeap", "lengthBytesUTF", "HEAP", "maxReadBytes", "__embind_register_void", "isVoid", "emval_symbols", "getStringOrSymbol", "address", "symbol", "emval_get_global", "globalThis", "__emval_get_global", "__emval_incref", "requireRegisteredType", "impl", "craftEmvalAllocator", "functionBody", "emval_newers", "<PERSON><PERSON><PERSON><PERSON>", "__emval_new", "newer", "_abort", "_emscripten_memcpy_big", "dest", "src", "num", "copyWithin", "emscripten_realloc_buffer", "grow", "byteLength", "_emscripten_resize_heap", "requestedSize", "oldSize", "maxHeapSize", "cutDown", "overGrownHeapSize", "Math", "min", "newSize", "max", "replacement", "p", "n", "m", "b", "h", "d", "j", "k", "l", "q", "g", "asm", "___wasm_call_ctors", "___embind_register_native_and_builtin_types", "calledRun", "runCaller", "run", "doRun", "setTimeout", "ready"], "mappings": "AAAA,kBAAkB;;;;+BAwkDlB;;;eAAA;;;AAvkDA,IAAIA,SAAS,AAAC;IACZ,OAAO,SAAUA,MAAM;QACrBA,SAASA,UAAU,CAAC;QAEpB,IAAIA,SAAS,OAAOA,WAAW,cAAcA,SAAS,CAAC;QACvD,IAAIC,qBAAqBC;QACzBF,MAAM,CAAC,QAAQ,GAAG,IAAIG,QAAQ,SAAUC,OAAO,EAAEC,MAAM;YACrDJ,sBAAsBG;YACtBF,qBAAqBG;QACvB;QACA,IAAIC,kBAAkB,CAAC;QACvB,IAAIC;QACJ,IAAKA,OAAOP,OAAQ;YAClB,IAAIA,OAAOQ,cAAc,CAACD,MAAM;gBAC9BD,eAAe,CAACC,IAAI,GAAGP,MAAM,CAACO,IAAI;YACpC;QACF;QACA,IAAIE,aAAa,EAAE;QACnB,IAAIC,cAAc;QAClB,IAAIC,QAAQ,SAAUC,MAAM,EAAEC,OAAO;YACnC,MAAMA;QACR;QACA,IAAIC,qBAAqB;QACzB,IAAIC,wBAAwB;QAC5B,IAAIC,sBAAsB;QAC1B,IAAIC,kBAAkB;QACtB,SAASC,WAAWC,IAAI;YACtB,IAAInB,MAAM,CAAC,aAAa,EAAE;gBACxB,OAAOA,MAAM,CAAC,aAAa,CAACmB,MAAMF;YACpC;YACA,OAAOA,kBAAkBE;QAC3B;QACA,IAAIC,OAAOC;QACX,IAAIC;QACJ,IAAIC;QACJ,IAAIP,qBAAqB;YACvB,IAAID,uBAAuB;gBACzBE,kBAAkBO,QAAQ,QAAQC,OAAO,CAACR,mBAAmB;YAC/D,OAAO;gBACLA,kBAAkBS,YAAY;YAChC;YACAN,QAAQ,SAASO,WAAWC,QAAQ,EAAEC,MAAM;gBAC1C,IAAI,CAACP,QAAQA,SAASE,QAAQ;gBAC9B,IAAI,CAACD,UAAUA,WAAWC,QAAQ;gBAClCI,WAAWL,QAAQ,CAAC,YAAY,CAACK;gBACjC,OAAON,MAAM,CAAC,eAAe,CAACM,UAAUC,SAAS,OAAO;YAC1D;YACAR,aAAa,SAASA,WAAWO,QAAQ;gBACvC,IAAIE,MAAMV,MAAMQ,UAAU;gBAC1B,IAAI,CAACE,IAAIC,MAAM,EAAE;oBACfD,MAAM,IAAIE,WAAWF;gBACvB;gBACAG,OAAOH,IAAIC,MAAM;gBACjB,OAAOD;YACT;YACA,IAAII,OAAO,CAAC,OAAO,CAACC,MAAM,GAAG,GAAG;gBAC9BzB,cAAcwB,OAAO,CAAC,OAAO,CAAC,EAAE,CAACE,OAAO,CAAC,OAAO;YAClD;YACA3B,aAAayB,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC;YACnC1B,QAAQ,SAAUC,MAAM;gBACtBsB,OAAO,CAAC,OAAO,CAACtB;YAClB;YACAZ,MAAM,CAAC,UAAU,GAAG;gBAClB,OAAO;YACT;QACF,OAAO,CACP;QACA,IAAIsC,MAAMtC,MAAM,CAAC,QAAQ,IAAIuC,QAAQC,GAAG,CAACC,IAAI,CAACF;QAC9C,IAAIG,MAAM1C,MAAM,CAAC,WAAW,IAAIuC,QAAQI,IAAI,CAACF,IAAI,CAACF;QAClD,IAAKhC,OAAOD,gBAAiB;YAC3B,IAAIA,gBAAgBE,cAAc,CAACD,MAAM;gBACvCP,MAAM,CAACO,IAAI,GAAGD,eAAe,CAACC,IAAI;YACpC;QACF;QACAD,kBAAkB;QAClB,IAAIN,MAAM,CAAC,YAAY,EAAES,aAAaT,MAAM,CAAC,YAAY;QACzD,IAAIA,MAAM,CAAC,cAAc,EAAEU,cAAcV,MAAM,CAAC,cAAc;QAC9D,IAAIA,MAAM,CAAC,OAAO,EAAEW,QAAQX,MAAM,CAAC,OAAO;QAC1C,IAAI4C;QACJ,IAAI5C,MAAM,CAAC,aAAa,EAAE4C,aAAa5C,MAAM,CAAC,aAAa;QAC3D,IAAI6C,gBAAgB7C,MAAM,CAAC,gBAAgB,IAAI;QAC/C,IAAI,OAAO8C,gBAAgB,UAAU;YACnCC,MAAM;QACR;QACA,IAAIC;QACJ,IAAIC,QAAQ;QACZ,IAAIC;QACJ,SAASjB,OAAOkB,SAAS,EAAEC,IAAI;YAC7B,IAAI,CAACD,WAAW;gBACdJ,MAAM,uBAAuBK;YAC/B;QACF;QACA,IAAIC,cAAc,IAAIC,YAAY;QAClC,SAASC,aAAaC,GAAG,EAAEC,cAAc;YACvC,IAAI,CAACD,KAAK,OAAO;YACjB,IAAIE,SAASF,MAAMC;YACnB,IAAK,IAAIE,MAAMH,KAAK,CAAEG,CAAAA,OAAOD,MAAK,KAAME,MAAM,CAACD,IAAI,EAAI,EAAEA;YACzD,OAAON,YAAYQ,MAAM,CAACD,OAAOE,QAAQ,CAACN,KAAKG;QACjD;QACA,SAASI,kBAAkBC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,eAAe;YAC3D,IAAI,CAAEA,CAAAA,kBAAkB,CAAA,GAAI,OAAO;YACnC,IAAIC,WAAWF;YACf,IAAIG,SAASH,SAASC,kBAAkB;YACxC,IAAK,IAAIG,IAAI,GAAGA,IAAIN,IAAI7B,MAAM,EAAE,EAAEmC,EAAG;gBACnC,IAAIC,IAAIP,IAAIQ,UAAU,CAACF;gBACvB,IAAIC,KAAK,SAASA,KAAK,OAAO;oBAC5B,IAAIE,KAAKT,IAAIQ,UAAU,CAAC,EAAEF;oBAC1BC,IAAI,AAAC,QAAS,CAAA,AAACA,CAAAA,IAAI,IAAG,KAAM,EAAC,IAAOE,KAAK;gBAC3C;gBACA,IAAIF,KAAK,KAAK;oBACZ,IAAIL,UAAUG,QAAQ;oBACtBJ,IAAI,CAACC,SAAS,GAAGK;gBACnB,OAAO,IAAIA,KAAK,MAAM;oBACpB,IAAIL,SAAS,KAAKG,QAAQ;oBAC1BJ,IAAI,CAACC,SAAS,GAAG,MAAOK,KAAK;oBAC7BN,IAAI,CAACC,SAAS,GAAG,MAAOK,IAAI;gBAC9B,OAAO,IAAIA,KAAK,OAAO;oBACrB,IAAIL,SAAS,KAAKG,QAAQ;oBAC1BJ,IAAI,CAACC,SAAS,GAAG,MAAOK,KAAK;oBAC7BN,IAAI,CAACC,SAAS,GAAG,MAAO,AAACK,KAAK,IAAK;oBACnCN,IAAI,CAACC,SAAS,GAAG,MAAOK,IAAI;gBAC9B,OAAO;oBACL,IAAIL,SAAS,KAAKG,QAAQ;oBAC1BJ,IAAI,CAACC,SAAS,GAAG,MAAOK,KAAK;oBAC7BN,IAAI,CAACC,SAAS,GAAG,MAAO,AAACK,KAAK,KAAM;oBACpCN,IAAI,CAACC,SAAS,GAAG,MAAO,AAACK,KAAK,IAAK;oBACnCN,IAAI,CAACC,SAAS,GAAG,MAAOK,IAAI;gBAC9B;YACF;YACAN,IAAI,CAACC,OAAO,GAAG;YACf,OAAOA,SAASE;QAClB;QACA,SAASM,aAAaV,GAAG,EAAEW,MAAM,EAAER,eAAe;YAChD,OAAOJ,kBAAkBC,KAAKJ,QAAQe,QAAQR;QAChD;QACA,SAASS,gBAAgBZ,GAAG;YAC1B,IAAIa,MAAM;YACV,IAAK,IAAIP,IAAI,GAAGA,IAAIN,IAAI7B,MAAM,EAAE,EAAEmC,EAAG;gBACnC,IAAIC,IAAIP,IAAIQ,UAAU,CAACF;gBACvB,IAAIC,KAAK,SAASA,KAAK,OACrBA,IAAI,AAAC,QAAS,CAAA,AAACA,CAAAA,IAAI,IAAG,KAAM,EAAC,IAAOP,IAAIQ,UAAU,CAAC,EAAEF,KAAK;gBAC5D,IAAIC,KAAK,KAAK,EAAEM;qBACX,IAAIN,KAAK,MAAMM,OAAO;qBACtB,IAAIN,KAAK,OAAOM,OAAO;qBACvBA,OAAO;YACd;YACA,OAAOA;QACT;QACA,IAAIC,eAAe,IAAIxB,YAAY;QACnC,SAASyB,cAAcvB,GAAG,EAAEC,cAAc;YACxC,IAAIuB,SAASxB;YACb,IAAIyB,MAAMD,UAAU;YACpB,IAAIE,SAASD,MAAMxB,iBAAiB;YACpC,MAAO,CAAEwB,CAAAA,OAAOC,MAAK,KAAMC,OAAO,CAACF,IAAI,CAAE,EAAEA;YAC3CD,SAASC,OAAO;YAChB,OAAOH,aAAajB,MAAM,CAACD,OAAOE,QAAQ,CAACN,KAAKwB;YAChD,IAAIhB,MAAM;YACV,IAAK,IAAIM,IAAI,GAAG,CAAEA,CAAAA,KAAKb,iBAAiB,CAAA,GAAI,EAAEa,EAAG;gBAC/C,IAAIc,WAAWC,MAAM,CAAC,AAAC7B,MAAMc,IAAI,KAAM,EAAE;gBACzC,IAAIc,YAAY,GAAG;gBACnBpB,OAAOsB,OAAOC,YAAY,CAACH;YAC7B;YACA,OAAOpB;QACT;QACA,SAASwB,cAAcxB,GAAG,EAAEW,MAAM,EAAER,eAAe;YACjD,IAAIA,oBAAoBsB,WAAW;gBACjCtB,kBAAkB;YACpB;YACA,IAAIA,kBAAkB,GAAG,OAAO;YAChCA,mBAAmB;YACnB,IAAIuB,WAAWf;YACf,IAAIgB,kBACFxB,kBAAkBH,IAAI7B,MAAM,GAAG,IAAIgC,kBAAkB,IAAIH,IAAI7B,MAAM;YACrE,IAAK,IAAImC,IAAI,GAAGA,IAAIqB,iBAAiB,EAAErB,EAAG;gBACxC,IAAIc,WAAWpB,IAAIQ,UAAU,CAACF;gBAC9Be,MAAM,CAACV,UAAU,EAAE,GAAGS;gBACtBT,UAAU;YACZ;YACAU,MAAM,CAACV,UAAU,EAAE,GAAG;YACtB,OAAOA,SAASe;QAClB;QACA,SAASE,iBAAiB5B,GAAG;YAC3B,OAAOA,IAAI7B,MAAM,GAAG;QACtB;QACA,SAAS0D,cAAcrC,GAAG,EAAEC,cAAc;YACxC,IAAIa,IAAI;YACR,IAAIN,MAAM;YACV,MAAO,CAAEM,CAAAA,KAAKb,iBAAiB,CAAA,EAAI;gBACjC,IAAIqC,QAAQC,MAAM,CAAC,AAACvC,MAAMc,IAAI,KAAM,EAAE;gBACtC,IAAIwB,SAAS,GAAG;gBAChB,EAAExB;gBACF,IAAIwB,SAAS,OAAO;oBAClB,IAAIE,KAAKF,QAAQ;oBACjB9B,OAAOsB,OAAOC,YAAY,CAAC,QAASS,MAAM,IAAK,QAASA,KAAK;gBAC/D,OAAO;oBACLhC,OAAOsB,OAAOC,YAAY,CAACO;gBAC7B;YACF;YACA,OAAO9B;QACT;QACA,SAASiC,cAAcjC,GAAG,EAAEW,MAAM,EAAER,eAAe;YACjD,IAAIA,oBAAoBsB,WAAW;gBACjCtB,kBAAkB;YACpB;YACA,IAAIA,kBAAkB,GAAG,OAAO;YAChC,IAAIuB,WAAWf;YACf,IAAIK,SAASU,WAAWvB,kBAAkB;YAC1C,IAAK,IAAIG,IAAI,GAAGA,IAAIN,IAAI7B,MAAM,EAAE,EAAEmC,EAAG;gBACnC,IAAIc,WAAWpB,IAAIQ,UAAU,CAACF;gBAC9B,IAAIc,YAAY,SAASA,YAAY,OAAO;oBAC1C,IAAIc,iBAAiBlC,IAAIQ,UAAU,CAAC,EAAEF;oBACtCc,WACE,AAAC,QAAS,CAAA,AAACA,CAAAA,WAAW,IAAG,KAAM,EAAC,IAAOc,iBAAiB;gBAC5D;gBACAH,MAAM,CAACpB,UAAU,EAAE,GAAGS;gBACtBT,UAAU;gBACV,IAAIA,SAAS,IAAIK,QAAQ;YAC3B;YACAe,MAAM,CAACpB,UAAU,EAAE,GAAG;YACtB,OAAOA,SAASe;QAClB;QACA,SAASS,iBAAiBnC,GAAG;YAC3B,IAAIa,MAAM;YACV,IAAK,IAAIP,IAAI,GAAGA,IAAIN,IAAI7B,MAAM,EAAE,EAAEmC,EAAG;gBACnC,IAAIc,WAAWpB,IAAIQ,UAAU,CAACF;gBAC9B,IAAIc,YAAY,SAASA,YAAY,OAAO,EAAEd;gBAC9CO,OAAO;YACT;YACA,OAAOA;QACT;QACA,SAASuB,QAAQC,CAAC,EAAEC,QAAQ;YAC1B,IAAID,IAAIC,WAAW,GAAG;gBACpBD,KAAKC,WAAYD,IAAIC;YACvB;YACA,OAAOD;QACT;QACA,IAAItE,QACFwE,OACA3C,QACAyB,QACAF,SACAY,QACAS,SACAC,SACAC;QACF,SAASC,2BAA2BC,GAAG;YACrC7E,SAAS6E;YACT5G,MAAM,CAAC,QAAQ,GAAGuG,QAAQ,IAAIM,UAAUD;YACxC5G,MAAM,CAAC,SAAS,GAAGqF,SAAS,IAAIyB,WAAWF;YAC3C5G,MAAM,CAAC,SAAS,GAAG+F,SAAS,IAAIgB,WAAWH;YAC3C5G,MAAM,CAAC,SAAS,GAAG4D,SAAS,IAAI5B,WAAW4E;YAC3C5G,MAAM,CAAC,UAAU,GAAGmF,UAAU,IAAI6B,YAAYJ;YAC9C5G,MAAM,CAAC,UAAU,GAAGwG,UAAU,IAAIS,YAAYL;YAC9C5G,MAAM,CAAC,UAAU,GAAGyG,UAAU,IAAIS,aAAaN;YAC/C5G,MAAM,CAAC,UAAU,GAAG0G,UAAU,IAAIS,aAAaP;QACjD;QACA,IAAIQ,iBAAiBpH,MAAM,CAAC,iBAAiB,IAAI;QACjD,IAAIqH;QACJ,IAAIC,eAAe,EAAE;QACrB,IAAIC,aAAa,EAAE;QACnB,IAAIC,gBAAgB,EAAE;QACtB,IAAIC,qBAAqB;QACzB,SAASC;YACP,IAAI1H,MAAM,CAAC,SAAS,EAAE;gBACpB,IAAI,OAAOA,MAAM,CAAC,SAAS,IAAI,YAC7BA,MAAM,CAAC,SAAS,GAAG;oBAACA,MAAM,CAAC,SAAS;iBAAC;gBACvC,MAAOA,MAAM,CAAC,SAAS,CAACmC,MAAM,CAAE;oBAC9BwF,YAAY3H,MAAM,CAAC,SAAS,CAAC4H,KAAK;gBACpC;YACF;YACAC,qBAAqBP;QACvB;QACA,SAASQ;YACPL,qBAAqB;YACrBI,qBAAqBN;QACvB;QACA,SAASQ;YACP,IAAI/H,MAAM,CAAC,UAAU,EAAE;gBACrB,IAAI,OAAOA,MAAM,CAAC,UAAU,IAAI,YAC9BA,MAAM,CAAC,UAAU,GAAG;oBAACA,MAAM,CAAC,UAAU;iBAAC;gBACzC,MAAOA,MAAM,CAAC,UAAU,CAACmC,MAAM,CAAE;oBAC/B6F,aAAahI,MAAM,CAAC,UAAU,CAAC4H,KAAK;gBACtC;YACF;YACAC,qBAAqBL;QACvB;QACA,SAASG,YAAYM,EAAE;YACrBX,aAAaY,OAAO,CAACD;QACvB;QACA,SAASE,UAAUF,EAAE;YACnBV,WAAWW,OAAO,CAACD;QACrB;QACA,SAASD,aAAaC,EAAE;YACtBT,cAAcU,OAAO,CAACD;QACxB;QACA,IAAIG,kBAAkB;QACtB,IAAIC,uBAAuB;QAC3B,IAAIC,wBAAwB;QAC5B,SAASC,iBAAiBC,EAAE;YAC1BJ;YACA,IAAIpI,MAAM,CAAC,yBAAyB,EAAE;gBACpCA,MAAM,CAAC,yBAAyB,CAACoI;YACnC;QACF;QACA,SAASK,oBAAoBD,EAAE;YAC7BJ;YACA,IAAIpI,MAAM,CAAC,yBAAyB,EAAE;gBACpCA,MAAM,CAAC,yBAAyB,CAACoI;YACnC;YACA,IAAIA,mBAAmB,GAAG;gBACxB,IAAIC,yBAAyB,MAAM;oBACjCK,cAAcL;oBACdA,uBAAuB;gBACzB;gBACA,IAAIC,uBAAuB;oBACzB,IAAIK,WAAWL;oBACfA,wBAAwB;oBACxBK;gBACF;YACF;QACF;QACA3I,MAAM,CAAC,kBAAkB,GAAG,CAAC;QAC7BA,MAAM,CAAC,kBAAkB,GAAG,CAAC;QAC7B,SAAS+C,MAAM6F,IAAI;YACjB,IAAI5I,MAAM,CAAC,UAAU,EAAE;gBACrBA,MAAM,CAAC,UAAU,CAAC4I;YACpB;YACAA,QAAQ;YACRlG,IAAIkG;YACJ3F,QAAQ;YACRC,aAAa;YACb0F,OAAO,WAAWA,OAAO;YACzB,IAAIC,IAAI,IAAI/F,YAAYgG,YAAY,CAACF;YACrC1I,mBAAmB2I;YACnB,MAAMA;QACR;QACA,IAAIE,gBAAgB;QACpB,SAASC,UAAUpH,QAAQ;YACzB,OAAOA,SAASqH,UAAU,CAACF;QAC7B;QACA,IAAI/I,MAAM,CAAC,aAAa,EAAE;YACxB,IAAIkJ,iBAAiB;YACrB,IAAI,CAACF,UAAUE,iBAAiB;gBAC9BA,iBAAiBhI,WAAWgI;YAC9B;QACF,OAAO;YACL,MAAM,IAAIC,MAAM;QAClB;QACA,SAASC,UAAUC,IAAI;YACrB,IAAI;gBACF,IAAIA,QAAQH,kBAAkBtG,YAAY;oBACxC,OAAO,IAAIZ,WAAWY;gBACxB;gBACA,IAAIvB,YAAY;oBACd,OAAOA,WAAWgI;gBACpB,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAO3G,KAAK;gBACZK,MAAML;YACR;QACF;QACA,SAAS4G;YACP,OAAOnJ,QAAQC,OAAO,GAAGmJ,IAAI,CAAC;gBAC5B,OAAOH,UAAUF;YACnB;QACF;QACA,SAASM;YACP,IAAIC,OAAO;gBAAEC,GAAGC;YAAc;YAC9B,SAASC,gBAAgBC,QAAQ,EAAEC,MAAM;gBACvC,IAAIC,WAAUF,SAASE,OAAO;gBAC9B/J,MAAM,CAAC,MAAM,GAAG+J;gBAChB/G,aAAahD,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC/B2G,2BAA2B3D,WAAWjB,MAAM;gBAC5CsF,YAAYrH,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC9BmI,UAAUnI,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC5ByI,oBAAoB;YACtB;YACAF,iBAAiB;YACjB,SAASyB,2BAA2BC,MAAM;gBACxCL,gBAAgBK,MAAM,CAAC,WAAW;YACpC;YACA,SAASC,uBAAuBC,QAAQ;gBACtC,OAAOb,mBACJC,IAAI,CAAC,SAAU1H,MAAM;oBACpB,IAAIoI,SAASnH,YAAYsH,WAAW,CAACvI,QAAQ4H;oBAC7C,OAAOQ;gBACT,GACCV,IAAI,CAACY,UAAU,SAAUE,MAAM;oBAC9B3H,IAAI,4CAA4C2H;oBAChDtH,MAAMsH;gBACR;YACJ;YACA,SAASC;gBACP,OAAOJ,uBAAuBF;YAChC;YACA,IAAIhK,MAAM,CAAC,kBAAkB,EAAE;gBAC7B,IAAI;oBACF,IAAI+J,WAAU/J,MAAM,CAAC,kBAAkB,CAACyJ,MAAMG;oBAC9C,OAAOG;gBACT,EAAE,OAAOlB,GAAG;oBACVnG,IAAI,wDAAwDmG;oBAC5D,OAAO;gBACT;YACF;YACAyB,mBAAmBC,KAAK,CAACrK;YACzB,OAAO,CAAC;QACV;QACA,SAAS2H,qBAAqB2C,SAAS;YACrC,MAAOA,UAAUrI,MAAM,GAAG,EAAG;gBAC3B,IAAIwG,WAAW6B,UAAU5C,KAAK;gBAC9B,IAAI,OAAOe,YAAY,YAAY;oBACjCA,SAAS3I;oBACT;gBACF;gBACA,IAAIyK,OAAO9B,SAAS8B,IAAI;gBACxB,IAAI,OAAOA,SAAS,UAAU;oBAC5B,IAAI9B,SAAS+B,GAAG,KAAKjF,WAAW;wBAC9B4B,UAAUsD,GAAG,CAACF;oBAChB,OAAO;wBACLpD,UAAUsD,GAAG,CAACF,MAAM9B,SAAS+B,GAAG;oBAClC;gBACF,OAAO;oBACLD,KAAK9B,SAAS+B,GAAG,KAAKjF,YAAY,OAAOkD,SAAS+B,GAAG;gBACvD;YACF;QACF;QACA,SAASE,QAAQH,IAAI,EAAEC,GAAG,GAAG;QAC7B,SAASG,qBAAqBC,EAAE,EAAEC,EAAE;YAClC,OAAOH,QAAQE,IAAIC;QACrB;QACA,SAASC,yBACPC,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,GACP;QACH,SAASC,iBAAiBH,IAAI;YAC5B,OAAQA;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,MAAM,IAAII,UAAU,wBAAwBJ;YAChD;QACF;QACA,SAASK;YACP,IAAIC,QAAQ,IAAIC,MAAM;YACtB,IAAK,IAAIpH,IAAI,GAAGA,IAAI,KAAK,EAAEA,EAAG;gBAC5BmH,KAAK,CAACnH,EAAE,GAAGgB,OAAOC,YAAY,CAACjB;YACjC;YACAqH,mBAAmBF;QACrB;QACA,IAAIE,mBAAmBlG;QACvB,SAASmG,iBAAiBpI,GAAG;YAC3B,IAAI1B,MAAM;YACV,IAAI+J,IAAIrI;YACR,MAAOI,MAAM,CAACiI,EAAE,CAAE;gBAChB/J,OAAO6J,gBAAgB,CAAC/H,MAAM,CAACiI,IAAI,CAAC;YACtC;YACA,OAAO/J;QACT;QACA,IAAIgK,uBAAuB,CAAC;QAC5B,IAAIC,kBAAkB,CAAC;QACvB,IAAIC,mBAAmB,CAAC;QACxB,IAAIC,SAAS;QACb,IAAIC,SAAS;QACb,SAASC,sBAAsBjB,IAAI;YACjC,IAAIzF,cAAcyF,MAAM;gBACtB,OAAO;YACT;YACAA,OAAOA,KAAK9I,OAAO,CAAC,kBAAkB;YACtC,IAAIgK,IAAIlB,KAAK1G,UAAU,CAAC;YACxB,IAAI4H,KAAKH,UAAUG,KAAKF,QAAQ;gBAC9B,OAAO,MAAMhB;YACf,OAAO;gBACL,OAAOA;YACT;QACF;QACA,SAASmB,oBAAoBnB,IAAI,EAAEoB,IAAI;YACrCpB,OAAOiB,sBAAsBjB;YAC7B,OAAO,IAAIqB,SACT,QACA,qBACErB,OACA,WACA,sBACA,8CACA,QACFoB;QACJ;QACA,SAASE,YAAYC,aAAa,EAAEC,SAAS;YAC3C,IAAIC,aAAaN,oBAAoBK,WAAW,SAAUE,OAAO;gBAC/D,IAAI,CAAC1B,IAAI,GAAGwB;gBACZ,IAAI,CAACE,OAAO,GAAGA;gBACf,IAAIC,QAAQ,IAAI1D,MAAMyD,SAASC,KAAK;gBACpC,IAAIA,UAAUpH,WAAW;oBACvB,IAAI,CAACoH,KAAK,GACR,IAAI,CAACC,QAAQ,KAAK,OAAOD,MAAMzK,OAAO,CAAC,sBAAsB;gBACjE;YACF;YACAuK,WAAWI,SAAS,GAAGC,OAAOC,MAAM,CAACR,cAAcM,SAAS;YAC5DJ,WAAWI,SAAS,CAACG,WAAW,GAAGP;YACnCA,WAAWI,SAAS,CAACD,QAAQ,GAAG;gBAC9B,IAAI,IAAI,CAACF,OAAO,KAAKnH,WAAW;oBAC9B,OAAO,IAAI,CAACyF,IAAI;gBAClB,OAAO;oBACL,OAAO,IAAI,CAACA,IAAI,GAAG,OAAO,IAAI,CAAC0B,OAAO;gBACxC;YACF;YACA,OAAOD;QACT;QACA,IAAIQ,eAAe1H;QACnB,SAAS2H,kBAAkBR,OAAO;YAChC,MAAM,IAAIO,aAAaP;QACzB;QACA,IAAIS,gBAAgB5H;QACpB,SAAS6H,mBAAmBV,OAAO;YACjC,MAAM,IAAIS,cAAcT;QAC1B;QACA,SAASW,8BACPC,OAAO,EACPC,cAAc,EACdC,iBAAiB;YAEjBF,QAAQG,OAAO,CAAC,SAAUC,IAAI;gBAC5B5B,gBAAgB,CAAC4B,KAAK,GAAGH;YAC3B;YACA,SAASI,WAAWC,cAAc;gBAChC,IAAIC,mBAAmBL,kBAAkBI;gBACzC,IAAIC,iBAAiB5L,MAAM,KAAKqL,QAAQrL,MAAM,EAAE;oBAC9CmL,mBAAmB;gBACrB;gBACA,IAAK,IAAIhJ,IAAI,GAAGA,IAAIkJ,QAAQrL,MAAM,EAAE,EAAEmC,EAAG;oBACvC0J,aAAaR,OAAO,CAAClJ,EAAE,EAAEyJ,gBAAgB,CAACzJ,EAAE;gBAC9C;YACF;YACA,IAAIwJ,iBAAiB,IAAIpC,MAAM+B,eAAetL,MAAM;YACpD,IAAI8L,oBAAoB,EAAE;YAC1B,IAAIC,aAAa;YACjBT,eAAeE,OAAO,CAAC,SAAUQ,EAAE,EAAE7J,CAAC;gBACpC,IAAIyH,gBAAgBvL,cAAc,CAAC2N,KAAK;oBACtCL,cAAc,CAACxJ,EAAE,GAAGyH,eAAe,CAACoC,GAAG;gBACzC,OAAO;oBACLF,kBAAkBG,IAAI,CAACD;oBACvB,IAAI,CAACrC,qBAAqBtL,cAAc,CAAC2N,KAAK;wBAC5CrC,oBAAoB,CAACqC,GAAG,GAAG,EAAE;oBAC/B;oBACArC,oBAAoB,CAACqC,GAAG,CAACC,IAAI,CAAC;wBAC5BN,cAAc,CAACxJ,EAAE,GAAGyH,eAAe,CAACoC,GAAG;wBACvC,EAAED;wBACF,IAAIA,eAAeD,kBAAkB9L,MAAM,EAAE;4BAC3C0L,WAAWC;wBACb;oBACF;gBACF;YACF;YACA,IAAI,MAAMG,kBAAkB9L,MAAM,EAAE;gBAClC0L,WAAWC;YACb;QACF;QACA,SAASE,aAAaK,OAAO,EAAEC,kBAAkB,EAAEC,OAAO;YACxDA,UAAUA,WAAW,CAAC;YACtB,IAAI,CAAE,CAAA,oBAAoBD,kBAAiB,GAAI;gBAC7C,MAAM,IAAI/C,UACR;YAEJ;YACA,IAAIL,OAAOoD,mBAAmBpD,IAAI;YAClC,IAAI,CAACmD,SAAS;gBACZjB,kBACE,WAAWlC,OAAO;YAEtB;YACA,IAAIa,gBAAgBvL,cAAc,CAAC6N,UAAU;gBAC3C,IAAIE,QAAQC,4BAA4B,EAAE;oBACxC;gBACF,OAAO;oBACLpB,kBAAkB,2BAA2BlC,OAAO;gBACtD;YACF;YACAa,eAAe,CAACsC,QAAQ,GAAGC;YAC3B,OAAOtC,gBAAgB,CAACqC,QAAQ;YAChC,IAAIvC,qBAAqBtL,cAAc,CAAC6N,UAAU;gBAChD,IAAI7D,YAAYsB,oBAAoB,CAACuC,QAAQ;gBAC7C,OAAOvC,oBAAoB,CAACuC,QAAQ;gBACpC7D,UAAUmD,OAAO,CAAC,SAAU1F,EAAE;oBAC5BA;gBACF;YACF;QACF;QACA,SAASwG,uBACPJ,OAAO,EACPnD,IAAI,EACJC,IAAI,EACJuD,SAAS,EACTC,UAAU;YAEV,IAAI/G,QAAQ0D,iBAAiBH;YAC7BD,OAAOU,iBAAiBV;YACxB8C,aAAaK,SAAS;gBACpBnD,MAAMA;gBACN0D,cAAc,SAAUC,EAAE;oBACxB,OAAO,CAAC,CAACA;gBACX;gBACAC,YAAY,SAAUC,WAAW,EAAEC,CAAC;oBAClC,OAAOA,IAAIN,YAAYC;gBACzB;gBACAM,gBAAgB;gBAChBC,sBAAsB,SAAUC,OAAO;oBACrC,IAAIlL;oBACJ,IAAIkH,SAAS,GAAG;wBACdlH,OAAOsC;oBACT,OAAO,IAAI4E,SAAS,GAAG;wBACrBlH,OAAOoB;oBACT,OAAO,IAAI8F,SAAS,GAAG;wBACrBlH,OAAO8B;oBACT,OAAO;wBACL,MAAM,IAAIwF,UAAU,gCAAgCL;oBACtD;oBACA,OAAO,IAAI,CAAC,eAAe,CAACjH,IAAI,CAACkL,WAAWvH,MAAM;gBACpD;gBACAwH,oBAAoB;YACtB;QACF;QACA,IAAIC,kBAAkB,EAAE;QACxB,IAAIC,qBAAqB;YACvB,CAAC;YACD;gBAAEC,OAAO9J;YAAU;YACnB;gBAAE8J,OAAO;YAAK;YACd;gBAAEA,OAAO;YAAK;YACd;gBAAEA,OAAO;YAAM;SAChB;QACD,SAASC,eAAeC,MAAM;YAC5B,IAAIA,SAAS,KAAK,MAAM,EAAEH,kBAAkB,CAACG,OAAO,CAACC,QAAQ,EAAE;gBAC7DJ,kBAAkB,CAACG,OAAO,GAAGhK;gBAC7B4J,gBAAgBjB,IAAI,CAACqB;YACvB;QACF;QACA,SAASE;YACP,IAAIC,QAAQ;YACZ,IAAK,IAAItL,IAAI,GAAGA,IAAIgL,mBAAmBnN,MAAM,EAAE,EAAEmC,EAAG;gBAClD,IAAIgL,kBAAkB,CAAChL,EAAE,KAAKmB,WAAW;oBACvC,EAAEmK;gBACJ;YACF;YACA,OAAOA;QACT;QACA,SAASC;YACP,IAAK,IAAIvL,IAAI,GAAGA,IAAIgL,mBAAmBnN,MAAM,EAAE,EAAEmC,EAAG;gBAClD,IAAIgL,kBAAkB,CAAChL,EAAE,KAAKmB,WAAW;oBACvC,OAAO6J,kBAAkB,CAAChL,EAAE;gBAC9B;YACF;YACA,OAAO;QACT;QACA,SAASwL;YACP9P,MAAM,CAAC,sBAAsB,GAAG2P;YAChC3P,MAAM,CAAC,kBAAkB,GAAG6P;QAC9B;QACA,SAASE,iBAAiBR,KAAK;YAC7B,OAAQA;gBACN,KAAK9J;oBAAW;wBACd,OAAO;oBACT;gBACA,KAAK;oBAAM;wBACT,OAAO;oBACT;gBACA,KAAK;oBAAM;wBACT,OAAO;oBACT;gBACA,KAAK;oBAAO;wBACV,OAAO;oBACT;gBACA;oBAAS;wBACP,IAAIgK,SAASJ,gBAAgBlN,MAAM,GAC/BkN,gBAAgBW,GAAG,KACnBV,mBAAmBnN,MAAM;wBAC7BmN,kBAAkB,CAACG,OAAO,GAAG;4BAAEC,UAAU;4BAAGH,OAAOA;wBAAM;wBACzD,OAAOE;oBACT;YACF;QACF;QACA,SAASQ,2BAA2Bd,OAAO;YACzC,OAAO,IAAI,CAAC,eAAe,CAAC3I,OAAO,CAAC2I,WAAW,EAAE;QACnD;QACA,SAASe,wBAAwB7B,OAAO,EAAEnD,IAAI;YAC5CA,OAAOU,iBAAiBV;YACxB8C,aAAaK,SAAS;gBACpBnD,MAAMA;gBACN0D,cAAc,SAAUa,MAAM;oBAC5B,IAAIU,KAAKb,kBAAkB,CAACG,OAAO,CAACF,KAAK;oBACzCC,eAAeC;oBACf,OAAOU;gBACT;gBACArB,YAAY,SAAUC,WAAW,EAAEQ,KAAK;oBACtC,OAAOQ,iBAAiBR;gBAC1B;gBACAN,gBAAgB;gBAChBC,sBAAsBe;gBACtBb,oBAAoB;YACtB;QACF;QACA,SAASgB,aAAaC,CAAC;YACrB,IAAIA,MAAM,MAAM;gBACd,OAAO;YACT;YACA,IAAIC,IAAI,OAAOD;YACf,IAAIC,MAAM,YAAYA,MAAM,WAAWA,MAAM,YAAY;gBACvD,OAAOD,EAAEvD,QAAQ;YACnB,OAAO;gBACL,OAAO,KAAKuD;YACd;QACF;QACA,SAASE,0BAA0BrF,IAAI,EAAEtD,KAAK;YAC5C,OAAQA;gBACN,KAAK;oBACH,OAAO,SAAUuH,OAAO;wBACtB,OAAO,IAAI,CAAC,eAAe,CAAC1I,OAAO,CAAC0I,WAAW,EAAE;oBACnD;gBACF,KAAK;oBACH,OAAO,SAAUA,OAAO;wBACtB,OAAO,IAAI,CAAC,eAAe,CAACzI,OAAO,CAACyI,WAAW,EAAE;oBACnD;gBACF;oBACE,MAAM,IAAI5D,UAAU,yBAAyBL;YACjD;QACF;QACA,SAASsF,wBAAwBnC,OAAO,EAAEnD,IAAI,EAAEC,IAAI;YAClD,IAAIvD,QAAQ0D,iBAAiBH;YAC7BD,OAAOU,iBAAiBV;YACxB8C,aAAaK,SAAS;gBACpBnD,MAAMA;gBACN0D,cAAc,SAAUW,KAAK;oBAC3B,OAAOA;gBACT;gBACAT,YAAY,SAAUC,WAAW,EAAEQ,KAAK;oBACtC,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,WAAW;wBAC3D,MAAM,IAAIhE,UACR,qBAAqB6E,aAAab,SAAS,UAAU,IAAI,CAACrE,IAAI;oBAElE;oBACA,OAAOqE;gBACT;gBACAN,gBAAgB;gBAChBC,sBAAsBqB,0BAA0BrF,MAAMtD;gBACtDwH,oBAAoB;YACtB;QACF;QACA,SAASqB,KAAKvD,WAAW,EAAEwD,YAAY;YACrC,IAAI,CAAExD,CAAAA,uBAAuBX,QAAO,GAAI;gBACtC,MAAM,IAAIhB,UACR,uCACE,OAAO2B,cACP;YAEN;YACA,IAAIyD,QAAQtE,oBACVa,YAAYhC,IAAI,IAAI,uBACpB,YAAa;YAEfyF,MAAM5D,SAAS,GAAGG,YAAYH,SAAS;YACvC,IAAI6D,MAAM,IAAID;YACd,IAAIE,IAAI3D,YAAY4D,KAAK,CAACF,KAAKF;YAC/B,OAAOG,aAAa7D,SAAS6D,IAAID;QACnC;QACA,SAASG,eAAehC,WAAW;YACjC,MAAOA,YAAY5M,MAAM,CAAE;gBACzB,IAAIqB,MAAMuL,YAAYiB,GAAG;gBACzB,IAAIgB,MAAMjC,YAAYiB,GAAG;gBACzBgB,IAAIxN;YACN;QACF;QACA,SAASyN,qBACPC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,aAAa;YAEb,IAAIC,WAAWJ,SAAShP,MAAM;YAC9B,IAAIoP,WAAW,GAAG;gBAChBnE,kBACE;YAEJ;YACA,IAAIoE,oBAAoBL,QAAQ,CAAC,EAAE,KAAK,QAAQC,cAAc;YAC9D,IAAIK,uBAAuB;YAC3B,IAAK,IAAInN,IAAI,GAAGA,IAAI6M,SAAShP,MAAM,EAAE,EAAEmC,EAAG;gBACxC,IACE6M,QAAQ,CAAC7M,EAAE,KAAK,QAChB6M,QAAQ,CAAC7M,EAAE,CAAC8K,kBAAkB,KAAK3J,WACnC;oBACAgM,uBAAuB;oBACvB;gBACF;YACF;YACA,IAAIC,UAAUP,QAAQ,CAAC,EAAE,CAACjG,IAAI,KAAK;YACnC,IAAIyG,WAAW;YACf,IAAIC,gBAAgB;YACpB,IAAK,IAAItN,IAAI,GAAGA,IAAIiN,WAAW,GAAG,EAAEjN,EAAG;gBACrCqN,YAAY,AAACrN,CAAAA,MAAM,IAAI,OAAO,EAAC,IAAK,QAAQA;gBAC5CsN,iBAAiB,AAACtN,CAAAA,MAAM,IAAI,OAAO,EAAC,IAAK,QAAQA,IAAI;YACvD;YACA,IAAIuN,gBACF,qBACA1F,sBAAsB+E,aACtB,MACAS,WACA,UACA,8BACCJ,CAAAA,WAAW,CAAA,IACZ,UACA,iCACAL,YACA,+DACCK,CAAAA,WAAW,CAAA,IACZ,gBACA;YACF,IAAIE,sBAAsB;gBACxBI,iBAAiB;YACnB;YACA,IAAIC,YAAYL,uBAAuB,gBAAgB;YACvD,IAAIM,QAAQ;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,IAAIC,QAAQ;gBACV5E;gBACAiE;gBACAC;gBACAP;gBACAI,QAAQ,CAAC,EAAE;gBACXA,QAAQ,CAAC,EAAE;aACZ;YACD,IAAIK,mBAAmB;gBACrBK,iBACE,2CAA2CC,YAAY;YAC3D;YACA,IAAK,IAAIxN,IAAI,GAAGA,IAAIiN,WAAW,GAAG,EAAEjN,EAAG;gBACrCuN,iBACE,YACAvN,IACA,oBACAA,IACA,iBACAwN,YACA,UACAxN,IACA,WACA6M,QAAQ,CAAC7M,IAAI,EAAE,CAAC4G,IAAI,GACpB;gBACF6G,MAAM3D,IAAI,CAAC,YAAY9J;gBACvB0N,MAAM5D,IAAI,CAAC+C,QAAQ,CAAC7M,IAAI,EAAE;YAC5B;YACA,IAAIkN,mBAAmB;gBACrBI,gBACE,cAAeA,CAAAA,cAAczP,MAAM,GAAG,IAAI,OAAO,EAAC,IAAKyP;YAC3D;YACAC,iBACE,AAACH,CAAAA,UAAU,cAAc,EAAC,IAC1B,eACCE,CAAAA,cAAczP,MAAM,GAAG,IAAI,OAAO,EAAC,IACpCyP,gBACA;YACF,IAAIH,sBAAsB;gBACxBI,iBAAiB;YACnB,OAAO;gBACL,IAAK,IAAIvN,IAAIkN,oBAAoB,IAAI,GAAGlN,IAAI6M,SAAShP,MAAM,EAAE,EAAEmC,EAAG;oBAChE,IAAI2N,YAAY3N,MAAM,IAAI,cAAc,QAASA,CAAAA,IAAI,CAAA,IAAK;oBAC1D,IAAI6M,QAAQ,CAAC7M,EAAE,CAAC8K,kBAAkB,KAAK,MAAM;wBAC3CyC,iBACEI,YACA,WACAA,YACA,WACAd,QAAQ,CAAC7M,EAAE,CAAC4G,IAAI,GAChB;wBACF6G,MAAM3D,IAAI,CAAC6D,YAAY;wBACvBD,MAAM5D,IAAI,CAAC+C,QAAQ,CAAC7M,EAAE,CAAC8K,kBAAkB;oBAC3C;gBACF;YACF;YACA,IAAIsC,SAAS;gBACXG,iBACE,0CAA0C;YAC9C,OAAO,CACP;YACAA,iBAAiB;YACjBE,MAAM3D,IAAI,CAACyD;YACX,IAAIK,kBAAkBzB,KAAKlE,UAAUwF,OAAOjB,KAAK,CAAC,MAAMkB;YACxD,OAAOE;QACT;QACA,SAASC,oBAAoBC,KAAK,EAAEC,UAAU,EAAEnB,SAAS;YACvD,IAAIzL,cAAc2M,KAAK,CAACC,WAAW,CAACC,aAAa,EAAE;gBACjD,IAAIC,WAAWH,KAAK,CAACC,WAAW;gBAChCD,KAAK,CAACC,WAAW,GAAG;oBAClB,IACE,CAACD,KAAK,CAACC,WAAW,CAACC,aAAa,CAAC9R,cAAc,CAACgS,UAAUrQ,MAAM,GAChE;wBACAiL,kBACE,eACE8D,YACA,mDACAsB,UAAUrQ,MAAM,GAChB,yBACAiQ,KAAK,CAACC,WAAW,CAACC,aAAa,GAC/B;oBAEN;oBACA,OAAOF,KAAK,CAACC,WAAW,CAACC,aAAa,CAACE,UAAUrQ,MAAM,CAAC,CAAC2O,KAAK,CAC5D,IAAI,EACJ0B;gBAEJ;gBACAJ,KAAK,CAACC,WAAW,CAACC,aAAa,GAAG,EAAE;gBACpCF,KAAK,CAACC,WAAW,CAACC,aAAa,CAACC,SAAShB,QAAQ,CAAC,GAAGgB;YACvD;QACF;QACA,SAASE,mBAAmBvH,IAAI,EAAEqE,KAAK,EAAEmD,YAAY;YACnD,IAAI1S,OAAOQ,cAAc,CAAC0K,OAAO;gBAC/B,IACEzF,cAAciN,gBACbjN,cAAczF,MAAM,CAACkL,KAAK,CAACoH,aAAa,IACvC7M,cAAczF,MAAM,CAACkL,KAAK,CAACoH,aAAa,CAACI,aAAa,EACxD;oBACAtF,kBAAkB,kCAAkClC,OAAO;gBAC7D;gBACAiH,oBAAoBnS,QAAQkL,MAAMA;gBAClC,IAAIlL,OAAOQ,cAAc,CAACkS,eAAe;oBACvCtF,kBACE,yFACEsF,eACA;gBAEN;gBACA1S,MAAM,CAACkL,KAAK,CAACoH,aAAa,CAACI,aAAa,GAAGnD;YAC7C,OAAO;gBACLvP,MAAM,CAACkL,KAAK,GAAGqE;gBACf,IAAI9J,cAAciN,cAAc;oBAC9B1S,MAAM,CAACkL,KAAK,CAACwH,YAAY,GAAGA;gBAC9B;YACF;QACF;QACA,SAASC,oBAAoB/C,KAAK,EAAEgD,YAAY;YAC9C,IAAIC,QAAQ,EAAE;YACd,IAAK,IAAIvO,IAAI,GAAGA,IAAIsL,OAAOtL,IAAK;gBAC9BuO,MAAMzE,IAAI,CAACrI,MAAM,CAAC,AAAC6M,CAAAA,gBAAgB,CAAA,IAAKtO,EAAE;YAC5C;YACA,OAAOuO;QACT;QACA,SAASC,oBAAoB5H,IAAI,EAAEqE,KAAK,EAAEmD,YAAY;YACpD,IAAI,CAAC1S,OAAOQ,cAAc,CAAC0K,OAAO;gBAChCoC,mBAAmB;YACrB;YACA,IACE7H,cAAczF,MAAM,CAACkL,KAAK,CAACoH,aAAa,IACxC7M,cAAciN,cACd;gBACA1S,MAAM,CAACkL,KAAK,CAACoH,aAAa,CAACI,aAAa,GAAGnD;YAC7C,OAAO;gBACLvP,MAAM,CAACkL,KAAK,GAAGqE;gBACfvP,MAAM,CAACkL,KAAK,CAACqG,QAAQ,GAAGmB;YAC1B;QACF;QACA,SAASK,cAAcC,GAAG,EAAExP,GAAG,EAAEyP,IAAI;YACnC,IAAI7G,IAAIpM,MAAM,CAAC,aAAagT,IAAI;YAChC,OAAOC,QAAQA,KAAK9Q,MAAM,GACtBiK,EAAE0E,KAAK,CAAC,MAAM;gBAACtN;aAAI,CAAC0P,MAAM,CAACD,SAC3B7G,EAAE+G,IAAI,CAAC,MAAM3P;QACnB;QACA,SAAS4P,QAAQJ,GAAG,EAAExP,GAAG,EAAEyP,IAAI;YAC7B,IAAID,IAAIK,QAAQ,CAAC,MAAM;gBACrB,OAAON,cAAcC,KAAKxP,KAAKyP;YACjC;YACA,OAAO5L,UAAUsD,GAAG,CAACnH,KAAKsN,KAAK,CAAC,MAAMmC;QACxC;QACA,SAASK,aAAaN,GAAG,EAAExP,GAAG;YAC5B,IAAI+P,WAAW,EAAE;YACjB,OAAO;gBACLA,SAASpR,MAAM,GAAGqQ,UAAUrQ,MAAM;gBAClC,IAAK,IAAImC,IAAI,GAAGA,IAAIkO,UAAUrQ,MAAM,EAAEmC,IAAK;oBACzCiP,QAAQ,CAACjP,EAAE,GAAGkO,SAAS,CAAClO,EAAE;gBAC5B;gBACA,OAAO8O,QAAQJ,KAAKxP,KAAK+P;YAC3B;QACF;QACA,SAASC,wBAAwBC,SAAS,EAAEC,WAAW;YACrDD,YAAY7H,iBAAiB6H;YAC7B,SAASE;gBACP,IAAIF,UAAUJ,QAAQ,CAAC,MAAM;oBAC3B,OAAOC,aAAaG,WAAWC;gBACjC;gBACA,OAAOrM,UAAUsD,GAAG,CAAC+I;YACvB;YACA,IAAIE,KAAKD;YACT,IAAI,OAAOC,OAAO,YAAY;gBAC5BxG,kBACE,6CACEqG,YACA,OACAC;YAEN;YACA,OAAOE;QACT;QACA,IAAIC,mBAAmBpO;QACvB,SAASqO,YAAYlG,IAAI;YACvB,IAAIpK,MAAMuQ,eAAenG;YACzB,IAAIuC,KAAKvE,iBAAiBpI;YAC1BwQ,MAAMxQ;YACN,OAAO2M;QACT;QACA,SAAS8D,sBAAsBrH,OAAO,EAAEsH,KAAK;YAC3C,IAAIC,eAAe,EAAE;YACrB,IAAIC,OAAO,CAAC;YACZ,SAASC,MAAMzG,IAAI;gBACjB,IAAIwG,IAAI,CAACxG,KAAK,EAAE;oBACd;gBACF;gBACA,IAAI7B,eAAe,CAAC6B,KAAK,EAAE;oBACzB;gBACF;gBACA,IAAI5B,gBAAgB,CAAC4B,KAAK,EAAE;oBAC1B5B,gBAAgB,CAAC4B,KAAK,CAACD,OAAO,CAAC0G;oBAC/B;gBACF;gBACAF,aAAa/F,IAAI,CAACR;gBAClBwG,IAAI,CAACxG,KAAK,GAAG;YACf;YACAsG,MAAMvG,OAAO,CAAC0G;YACd,MAAM,IAAIR,iBACRjH,UAAU,OAAOuH,aAAaG,GAAG,CAACR,aAAaS,IAAI,CAAC;gBAAC;aAAK;QAE9D;QACA,SAASC,2BACPtJ,IAAI,EACJqG,QAAQ,EACRkD,eAAe,EACfhB,SAAS,EACTiB,UAAU,EACVC,EAAE;YAEF,IAAIxD,WAAWwB,oBAAoBpB,UAAUkD;YAC7CvJ,OAAOU,iBAAiBV;YACxBwJ,aAAalB,wBAAwBC,WAAWiB;YAChDjC,mBACEvH,MACA;gBACE+I,sBACE,iBAAiB/I,OAAO,yBACxBiG;YAEJ,GACAI,WAAW;YAEbhE,8BAA8B,EAAE,EAAE4D,UAAU,SAAUA,QAAQ;gBAC5D,IAAIyD,mBAAmB;oBAACzD,QAAQ,CAAC,EAAE;oBAAE;iBAAK,CAAC+B,MAAM,CAAC/B,SAAS9O,KAAK,CAAC;gBACjEyQ,oBACE5H,MACA+F,qBAAqB/F,MAAM0J,kBAAkB,MAAMF,YAAYC,KAC/DpD,WAAW;gBAEb,OAAO,EAAE;YACX;QACF;QACA,SAASsD,4BAA4B3J,IAAI,EAAEtD,KAAK,EAAEkN,MAAM;YACtD,OAAQlN;gBACN,KAAK;oBACH,OAAOkN,SACH,SAASC,kBAAkB5F,OAAO;wBAChC,OAAO5I,KAAK,CAAC4I,QAAQ;oBACvB,IACA,SAAS6F,kBAAkB7F,OAAO;wBAChC,OAAOvL,MAAM,CAACuL,QAAQ;oBACxB;gBACN,KAAK;oBACH,OAAO2F,SACH,SAASG,mBAAmB9F,OAAO;wBACjC,OAAO9J,MAAM,CAAC8J,WAAW,EAAE;oBAC7B,IACA,SAAS+F,mBAAmB/F,OAAO;wBACjC,OAAOhK,OAAO,CAACgK,WAAW,EAAE;oBAC9B;gBACN,KAAK;oBACH,OAAO2F,SACH,SAASK,mBAAmBhG,OAAO;wBACjC,OAAOpJ,MAAM,CAACoJ,WAAW,EAAE;oBAC7B,IACA,SAASiG,mBAAmBjG,OAAO;wBACjC,OAAO3I,OAAO,CAAC2I,WAAW,EAAE;oBAC9B;gBACN;oBACE,MAAM,IAAI5D,UAAU,2BAA2BL;YACnD;QACF;QACA,SAASmK,0BACPpK,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ;YAERH,OAAOU,iBAAiBV;YACxB,IAAIG,aAAa,CAAC,GAAG;gBACnBA,WAAW;YACb;YACA,IAAIzD,QAAQ0D,iBAAiBH;YAC7B,IAAIyD,eAAe,SAAUW,KAAK;gBAChC,OAAOA;YACT;YACA,IAAInE,aAAa,GAAG;gBAClB,IAAIkK,WAAW,KAAK,IAAInK;gBACxByD,eAAe,SAAUW,KAAK;oBAC5B,OAAO,AAACA,SAAS+F,aAAcA;gBACjC;YACF;YACA,IAAIC,iBAAiBrK,KAAKmI,QAAQ,CAAC;YACnCrF,aAAa/C,eAAe;gBAC1BC,MAAMA;gBACN0D,cAAcA;gBACdE,YAAY,SAAUC,WAAW,EAAEQ,KAAK;oBACtC,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,WAAW;wBAC3D,MAAM,IAAIhE,UACR,qBAAqB6E,aAAab,SAAS,UAAU,IAAI,CAACrE,IAAI;oBAElE;oBACA,IAAIqE,QAAQnE,YAAYmE,QAAQlE,UAAU;wBACxC,MAAM,IAAIE,UACR,uBACE6E,aAAab,SACb,0DACArE,OACA,0CACAE,WACA,OACAC,WACA;oBAEN;oBACA,OAAOkK,iBAAiBhG,UAAU,IAAIA,QAAQ;gBAChD;gBACAN,gBAAgB;gBAChBC,sBAAsB2F,4BACpB3J,MACAtD,OACAwD,aAAa;gBAEfgE,oBAAoB;YACtB;QACF;QACA,SAASoG,8BAA8BnH,OAAO,EAAEoH,aAAa,EAAEvK,IAAI;YACjE,IAAIwK,cAAc;gBAChB7O;gBACA7E;gBACA8E;gBACAE;gBACAD;gBACAE;gBACAC;gBACAC;aACD;YACD,IAAIwO,KAAKD,WAAW,CAACD,cAAc;YACnC,SAASG,iBAAiBnG,MAAM;gBAC9BA,SAASA,UAAU;gBACnB,IAAIxL,OAAOuC;gBACX,IAAI2E,OAAOlH,IAAI,CAACwL,OAAO;gBACvB,IAAIoG,OAAO5R,IAAI,CAACwL,SAAS,EAAE;gBAC3B,OAAO,IAAIkG,GAAG5T,QAAQ8T,MAAM1K;YAC9B;YACAD,OAAOU,iBAAiBV;YACxB8C,aACEK,SACA;gBACEnD,MAAMA;gBACN0D,cAAcgH;gBACd3G,gBAAgB;gBAChBC,sBAAsB0G;YACxB,GACA;gBAAEpH,8BAA8B;YAAK;QAEzC;QACA,SAASsH,6BAA6BzH,OAAO,EAAEnD,IAAI;YACjDA,OAAOU,iBAAiBV;YACxB,IAAI6K,kBAAkB7K,SAAS;YAC/B8C,aAAaK,SAAS;gBACpBnD,MAAMA;gBACN0D,cAAc,SAAUW,KAAK;oBAC3B,IAAIpN,SAASqE,OAAO,CAAC+I,SAAS,EAAE;oBAChC,IAAIvL;oBACJ,IAAI+R,iBAAiB;wBACnB,IAAIC,iBAAiBzG,QAAQ;wBAC7B,IAAK,IAAIjL,IAAI,GAAGA,KAAKnC,QAAQ,EAAEmC,EAAG;4BAChC,IAAI2R,iBAAiB1G,QAAQ,IAAIjL;4BACjC,IAAIA,KAAKnC,UAAUyB,MAAM,CAACqS,eAAe,IAAI,GAAG;gCAC9C,IAAIC,UAAUD,iBAAiBD;gCAC/B,IAAIG,gBAAgB5S,aAAayS,gBAAgBE;gCACjD,IAAIlS,QAAQyB,WAAW;oCACrBzB,MAAMmS;gCACR,OAAO;oCACLnS,OAAOsB,OAAOC,YAAY,CAAC;oCAC3BvB,OAAOmS;gCACT;gCACAH,iBAAiBC,iBAAiB;4BACpC;wBACF;oBACF,OAAO;wBACL,IAAIvM,IAAI,IAAIgC,MAAMvJ;wBAClB,IAAK,IAAImC,IAAI,GAAGA,IAAInC,QAAQ,EAAEmC,EAAG;4BAC/BoF,CAAC,CAACpF,EAAE,GAAGgB,OAAOC,YAAY,CAAC3B,MAAM,CAAC2L,QAAQ,IAAIjL,EAAE;wBAClD;wBACAN,MAAM0F,EAAE6K,IAAI,CAAC;oBACf;oBACAP,MAAMzE;oBACN,OAAOvL;gBACT;gBACA8K,YAAY,SAAUC,WAAW,EAAEQ,KAAK;oBACtC,IAAIA,iBAAiB6G,aAAa;wBAChC7G,QAAQ,IAAIvN,WAAWuN;oBACzB;oBACA,IAAI8G;oBACJ,IAAIC,sBAAsB,OAAO/G,UAAU;oBAC3C,IACE,CACE+G,CAAAA,uBACA/G,iBAAiBvN,cACjBuN,iBAAiBgH,qBACjBhH,iBAAiB1I,SAAQ,GAE3B;wBACAuG,kBAAkB;oBACpB;oBACA,IAAI2I,mBAAmBO,qBAAqB;wBAC1CD,YAAY;4BACV,OAAOzR,gBAAgB2K;wBACzB;oBACF,OAAO;wBACL8G,YAAY;4BACV,OAAO9G,MAAMpN,MAAM;wBACrB;oBACF;oBACA,IAAIA,SAASkU;oBACb,IAAI7S,MAAMgT,QAAQ,IAAIrU,SAAS;oBAC/BqE,OAAO,CAAChD,OAAO,EAAE,GAAGrB;oBACpB,IAAI4T,mBAAmBO,qBAAqB;wBAC1C5R,aAAa6K,OAAO/L,MAAM,GAAGrB,SAAS;oBACxC,OAAO;wBACL,IAAImU,qBAAqB;4BACvB,IAAK,IAAIhS,IAAI,GAAGA,IAAInC,QAAQ,EAAEmC,EAAG;gCAC/B,IAAImS,WAAWlH,MAAM/K,UAAU,CAACF;gCAChC,IAAImS,WAAW,KAAK;oCAClBzC,MAAMxQ;oCACN4J,kBACE;gCAEJ;gCACAxJ,MAAM,CAACJ,MAAM,IAAIc,EAAE,GAAGmS;4BACxB;wBACF,OAAO;4BACL,IAAK,IAAInS,IAAI,GAAGA,IAAInC,QAAQ,EAAEmC,EAAG;gCAC/BV,MAAM,CAACJ,MAAM,IAAIc,EAAE,GAAGiL,KAAK,CAACjL,EAAE;4BAChC;wBACF;oBACF;oBACA,IAAIyK,gBAAgB,MAAM;wBACxBA,YAAYX,IAAI,CAAC4F,OAAOxQ;oBAC1B;oBACA,OAAOA;gBACT;gBACAyL,gBAAgB;gBAChBC,sBAAsBe;gBACtBb,oBAAoB,SAAU5L,GAAG;oBAC/BwQ,MAAMxQ;gBACR;YACF;QACF;QACA,SAASkT,8BAA8BrI,OAAO,EAAEsI,QAAQ,EAAEzL,IAAI;YAC5DA,OAAOU,iBAAiBV;YACxB,IAAI0L,cAAcC,cAAcC,SAASC,gBAAgBnP;YACzD,IAAI+O,aAAa,GAAG;gBAClBC,eAAe7R;gBACf8R,eAAerR;gBACfuR,iBAAiBnR;gBACjBkR,UAAU;oBACR,OAAO3R;gBACT;gBACAyC,QAAQ;YACV,OAAO,IAAI+O,aAAa,GAAG;gBACzBC,eAAe/Q;gBACfgR,eAAe5Q;gBACf8Q,iBAAiB5Q;gBACjB2Q,UAAU;oBACR,OAAOtQ;gBACT;gBACAoB,QAAQ;YACV;YACAoG,aAAaK,SAAS;gBACpBnD,MAAMA;gBACN0D,cAAc,SAAUW,KAAK;oBAC3B,IAAIpN,SAASqE,OAAO,CAAC+I,SAAS,EAAE;oBAChC,IAAIyH,OAAOF;oBACX,IAAI9S;oBACJ,IAAIgS,iBAAiBzG,QAAQ;oBAC7B,IAAK,IAAIjL,IAAI,GAAGA,KAAKnC,QAAQ,EAAEmC,EAAG;wBAChC,IAAI2R,iBAAiB1G,QAAQ,IAAIjL,IAAIqS;wBACrC,IAAIrS,KAAKnC,UAAU6U,IAAI,CAACf,kBAAkBrO,MAAM,IAAI,GAAG;4BACrD,IAAIqP,eAAehB,iBAAiBD;4BACpC,IAAIG,gBAAgBS,aAAaZ,gBAAgBiB;4BACjD,IAAIjT,QAAQyB,WAAW;gCACrBzB,MAAMmS;4BACR,OAAO;gCACLnS,OAAOsB,OAAOC,YAAY,CAAC;gCAC3BvB,OAAOmS;4BACT;4BACAH,iBAAiBC,iBAAiBU;wBACpC;oBACF;oBACA3C,MAAMzE;oBACN,OAAOvL;gBACT;gBACA8K,YAAY,SAAUC,WAAW,EAAEQ,KAAK;oBACtC,IAAI,CAAE,CAAA,OAAOA,UAAU,QAAO,GAAI;wBAChCnC,kBACE,+CAA+ClC;oBAEnD;oBACA,IAAI/I,SAAS4U,eAAexH;oBAC5B,IAAI/L,MAAMgT,QAAQ,IAAIrU,SAASwU;oBAC/BnQ,OAAO,CAAChD,OAAO,EAAE,GAAGrB,UAAUyF;oBAC9BiP,aAAatH,OAAO/L,MAAM,GAAGrB,SAASwU;oBACtC,IAAI5H,gBAAgB,MAAM;wBACxBA,YAAYX,IAAI,CAAC4F,OAAOxQ;oBAC1B;oBACA,OAAOA;gBACT;gBACAyL,gBAAgB;gBAChBC,sBAAsBe;gBACtBb,oBAAoB,SAAU5L,GAAG;oBAC/BwQ,MAAMxQ;gBACR;YACF;QACF;QACA,SAAS0T,uBAAuB7I,OAAO,EAAEnD,IAAI;YAC3CA,OAAOU,iBAAiBV;YACxB8C,aAAaK,SAAS;gBACpB8I,QAAQ;gBACRjM,MAAMA;gBACN+D,gBAAgB;gBAChBL,cAAc;oBACZ,OAAOnJ;gBACT;gBACAqJ,YAAY,SAAUC,WAAW,EAAEC,CAAC;oBAClC,OAAOvJ;gBACT;YACF;QACF;QACA,IAAI2R,gBAAgB,CAAC;QACrB,SAASC,kBAAkBC,OAAO;YAChC,IAAIC,SAASH,aAAa,CAACE,QAAQ;YACnC,IAAIC,WAAW9R,WAAW;gBACxB,OAAOmG,iBAAiB0L;YAC1B,OAAO;gBACL,OAAOC;YACT;QACF;QACA,SAASC;YACP,IAAI,OAAOC,eAAe,UAAU;gBAClC,OAAOA;YACT;YACA,OAAO,AAAC,CAAA;gBACN,OAAOlL;YACT,CAAA,IAAK;QACP;QACA,SAASmL,mBAAmBxM,IAAI;YAC9B,IAAIA,SAAS,GAAG;gBACd,OAAO6E,iBAAiByH;YAC1B,OAAO;gBACLtM,OAAOmM,kBAAkBnM;gBACzB,OAAO6E,iBAAiByH,kBAAkB,CAACtM,KAAK;YAClD;QACF;QACA,SAASyM,eAAelI,MAAM;YAC5B,IAAIA,SAAS,GAAG;gBACdH,kBAAkB,CAACG,OAAO,CAACC,QAAQ,IAAI;YACzC;QACF;QACA,SAASkI,sBAAsBvJ,OAAO,EAAE6C,SAAS;YAC/C,IAAI2G,OAAO9L,eAAe,CAACsC,QAAQ;YACnC,IAAI5I,cAAcoS,MAAM;gBACtBzK,kBACE8D,YAAY,uBAAuB4C,YAAYzF;YAEnD;YACA,OAAOwJ;QACT;QACA,SAASC,oBAAoBvG,QAAQ;YACnC,IAAII,WAAW;YACf,IAAK,IAAIrN,IAAI,GAAGA,IAAIiN,UAAU,EAAEjN,EAAG;gBACjCqN,YAAY,AAACrN,CAAAA,MAAM,IAAI,OAAO,EAAC,IAAK,QAAQA;YAC9C;YACA,IAAIyT,eACF,qCACAxG,WACA;YACF,IAAK,IAAIjN,IAAI,GAAGA,IAAIiN,UAAU,EAAEjN,EAAG;gBACjCyT,gBACE,gBACAzT,IACA,kEACAA,IACA,mBACAA,IACA,UACA,YACAA,IACA,eACAA,IACA,mCACA,oBACAA,IACA;YACJ;YACAyT,gBACE,+BACApG,WACA,SACA,oCACA;YACF,OAAO,IAAIpF,SACT,yBACA,UACA,oBACAwL,cACAH,uBAAuB5X,QAAQ+P;QACnC;QACA,IAAIiI,eAAe,CAAC;QACpB,SAASC,cAAcxI,MAAM;YAC3B,IAAI,CAACA,QAAQ;gBACXrC,kBAAkB,sCAAsCqC;YAC1D;YACA,OAAOH,kBAAkB,CAACG,OAAO,CAACF,KAAK;QACzC;QACA,SAAS2I,YAAYzI,MAAM,EAAE8B,QAAQ,EAAEJ,QAAQ,EAAE8B,IAAI;YACnDxD,SAASwI,cAAcxI;YACvB,IAAI0I,QAAQH,YAAY,CAACzG,SAAS;YAClC,IAAI,CAAC4G,OAAO;gBACVA,QAAQL,oBAAoBvG;gBAC5ByG,YAAY,CAACzG,SAAS,GAAG4G;YAC3B;YACA,OAAOA,MAAM1I,QAAQ0B,UAAU8B;QACjC;QACA,SAASmF;YACPrV;QACF;QACA,SAASsV,uBAAuBC,IAAI,EAAEC,GAAG,EAAEC,GAAG;YAC5C5U,OAAO6U,UAAU,CAACH,MAAMC,KAAKA,MAAMC;QACrC;QACA,SAASE,0BAA0BvN,IAAI;YACrC,IAAI;gBACFnI,WAAW2V,IAAI,CAAC,AAACxN,OAAOpJ,OAAO6W,UAAU,GAAG,UAAW;gBACvDjS,2BAA2B3D,WAAWjB,MAAM;gBAC5C,OAAO;YACT,EAAE,OAAO8G,GAAG,CAAC;QACf;QACA,SAASgQ,wBAAwBC,aAAa;YAC5C,IAAIC,UAAUnV,OAAOzB,MAAM;YAC3B2W,gBAAgBA,kBAAkB;YAClC,IAAIE,cAAc;YAClB,IAAIF,gBAAgBE,aAAa;gBAC/B,OAAO;YACT;YACA,IAAK,IAAIC,UAAU,GAAGA,WAAW,GAAGA,WAAW,EAAG;gBAChD,IAAIC,oBAAoBH,UAAW,CAAA,IAAI,MAAME,OAAM;gBACnDC,oBAAoBC,KAAKC,GAAG,CAC1BF,mBACAJ,gBAAgB;gBAElB,IAAIO,UAAUF,KAAKC,GAAG,CACpBJ,aACA5S,QAAQ+S,KAAKG,GAAG,CAACR,eAAeI,oBAAoB;gBAEtD,IAAIK,cAAcb,0BAA0BW;gBAC5C,IAAIE,aAAa;oBACf,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA/N;QACA2B,eAAenN,MAAM,CAAC,eAAe,GAAGwM,YAAYrD,OAAO;QAC3DkE,gBAAgBrN,MAAM,CAAC,gBAAgB,GAAGwM,YACxCrD,OACA;QAEF2G;QACA+D,mBAAmB7T,MAAM,CAAC,mBAAmB,GAAGwM,YAC9CrD,OACA;QAEF,IAAIQ,gBAAgB;YAClBd,GAAGgC;YACH2O,GAAGxO;YACHyO,GAAGhL;YACHoC,GAAGX;YACHwJ,GAAGlJ;YACHlM,GAAGkQ;YACHmF,GAAGtE;YACH3L,GAAG8L;YACHoE,GAAG9D;YACH1J,GAAGsK;YACH1H,GAAGkI;YACHrL,GAAG2D;YACHqK,GAAGnC;YACHoC,GAAGnC;YACHoC,GAAG7B;YACH8B,GAAG5B;YACH6B,GAAG5B;YACH6B,GAAGrB;QACL;QACA,IAAIsB,MAAM3Q;QACV,IAAI4Q,qBAAsBpa,MAAM,CAAC,qBAAqB,GAAG;YACvD,OAAO,AAACoa,CAAAA,qBAAqBpa,MAAM,CAAC,qBAAqB,GACvDA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8Q,KAAK,CAAC,MAAM0B;QACpC;QACA,IAAIgE,UAAWxW,MAAM,CAAC,UAAU,GAAG;YACjC,OAAO,AAACwW,CAAAA,UAAUxW,MAAM,CAAC,UAAU,GAAGA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8Q,KAAK,CAC7D,MACA0B;QAEJ;QACA,IAAIwB,QAAShU,MAAM,CAAC,QAAQ,GAAG;YAC7B,OAAO,AAACgU,CAAAA,QAAQhU,MAAM,CAAC,QAAQ,GAAGA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8Q,KAAK,CACzD,MACA0B;QAEJ;QACA,IAAIuB,iBAAkB/T,MAAM,CAAC,iBAAiB,GAAG;YAC/C,OAAO,AAAC+T,CAAAA,iBAAiB/T,MAAM,CAAC,iBAAiB,GAC/CA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8Q,KAAK,CAAC,MAAM0B;QACpC;QACA,IAAI6H,8CAA+Cra,MAAM,CACvD,8CACD,GAAG;YACF,OAAO,AAACqa,CAAAA,8CAA8Cra,MAAM,CAC1D,8CACD,GACCA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8Q,KAAK,CAAC,MAAM0B;QACpC;QACA,IAAI8H;QACJhS,wBAAwB,SAASiS;YAC/B,IAAI,CAACD,WAAWE;YAChB,IAAI,CAACF,WAAWhS,wBAAwBiS;QAC1C;QACA,SAASC,IAAIvH,IAAI;YACfA,OAAOA,QAAQxS;YACf,IAAI2H,kBAAkB,GAAG;gBACvB;YACF;YACAV;YACA,IAAIU,kBAAkB,GAAG;gBACvB;YACF;YACA,SAASqS;gBACP,IAAIH,WAAW;gBACfA,YAAY;gBACZta,MAAM,CAAC,YAAY,GAAG;gBACtB,IAAIiD,OAAO;gBACX6E;gBACA7H,oBAAoBD;gBACpB,IAAIA,MAAM,CAAC,uBAAuB,EAAEA,MAAM,CAAC,uBAAuB;gBAClE+H;YACF;YACA,IAAI/H,MAAM,CAAC,YAAY,EAAE;gBACvBA,MAAM,CAAC,YAAY,CAAC;gBACpB0a,WAAW;oBACTA,WAAW;wBACT1a,MAAM,CAAC,YAAY,CAAC;oBACtB,GAAG;oBACHya;gBACF,GAAG;YACL,OAAO;gBACLA;YACF;QACF;QACAza,MAAM,CAAC,MAAM,GAAGwa;QAChB,IAAIxa,MAAM,CAAC,UAAU,EAAE;YACrB,IAAI,OAAOA,MAAM,CAAC,UAAU,IAAI,YAC9BA,MAAM,CAAC,UAAU,GAAG;gBAACA,MAAM,CAAC,UAAU;aAAC;YACzC,MAAOA,MAAM,CAAC,UAAU,CAACmC,MAAM,GAAG,EAAG;gBACnCnC,MAAM,CAAC,UAAU,CAACgQ,GAAG;YACvB;QACF;QACAwK;QAEA,OAAOxa,OAAO2a,KAAK;IACrB;AACF;MACA,WAAe3a"}