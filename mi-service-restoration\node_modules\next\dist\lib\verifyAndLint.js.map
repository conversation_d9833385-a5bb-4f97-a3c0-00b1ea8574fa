{"version": 3, "sources": ["../../src/lib/verifyAndLint.ts"], "names": ["verifyAndLint", "dir", "cacheLocation", "configLintDirs", "enableWorkerThreads", "telemetry", "lintWorkers", "Worker", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "lintDirs", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "join", "existsSync", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "eventLintCheckCompleted", "buildLint", "isError", "flush", "CompileError", "console", "log", "err", "type", "error", "red", "message", "process", "exit", "end"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;4BAVF;wBACG;oBACI;sBACN;2BACe;wBAEI;8BACX;gEACT;;;;;;AAEb,eAAeA,cACpBC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,mBAAwC,EACxCC,SAAoB;IAEpB,IAAIC;IAMJ,IAAI;QACFA,cAAc,IAAIC,cAAM,CAACC,QAAQC,OAAO,CAAC,0BAA0B;YACjEC,gBAAgB;gBAAC;aAAe;YAChCC,YAAY;YACZP;YACAQ,YAAY;QACd;QAIA,MAAMC,WAAW,AAACV,CAAAA,kBAAkBW,8BAAmB,AAAD,EAAGC,MAAM,CAC7D,CAACC,KAAeC;YACd,MAAMC,UAAUC,IAAAA,UAAI,EAAClB,KAAKgB;YAC1B,IAAI,CAACG,IAAAA,cAAU,EAACF,UAAU,OAAOF;YACjCA,IAAIK,IAAI,CAACH;YACT,OAAOF;QACT,GACA,EAAE;QAGJ,MAAMM,cAAc,OAAMhB,+BAAAA,YAAaiB,YAAY,CAACtB,KAAKY,UAAU;YACjEW,iBAAiB;YACjBC,eAAe;gBACbvB;YACF;QACF;QACA,MAAMwB,aACJ,OAAOJ,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaK,MAAM;QAErE,IAAI,OAAOL,gBAAgB,aAAYA,+BAAAA,YAAaM,SAAS,GAAE;YAC7DvB,UAAUwB,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtB,GAAGR,YAAYM,SAAS;gBACxBG,WAAW;YACb;QAEJ;QAEA,IAAI,OAAOT,gBAAgB,aAAYA,+BAAAA,YAAaU,OAAO,KAAIN,YAAY;YACzE,MAAMrB,UAAU4B,KAAK;YACrB,MAAM,IAAIC,0BAAY,CAACR;QACzB;QAEA,IAAIA,YAAY;YACdS,QAAQC,GAAG,CAACV;QACd;IACF,EAAE,OAAOW,KAAK;QACZ,IAAIL,IAAAA,gBAAO,EAACK,MAAM;YAChB,IAAIA,IAAIC,IAAI,KAAK,kBAAkBD,eAAeH,0BAAY,EAAE;gBAC9DC,QAAQI,KAAK,CAACC,IAAAA,eAAG,EAAC;gBAClBL,QAAQI,KAAK,CAACF,IAAII,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf,OAAO,IAAIN,IAAIC,IAAI,KAAK,cAAc;gBACpCH,QAAQI,KAAK,CAACF,IAAII,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf;QACF;QACA,MAAMN;IACR,SAAU;QACR,IAAI;YACF/B,+BAAAA,YAAasC,GAAG;QAClB,EAAE,OAAM,CAAC;IACX;AACF"}